package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// DecisionSearchRequest represents the request payload for decision search
type DecisionSearchRequest struct {
	Data DecisionSearchData `json:"data"`
}

// DecisionSearchData represents the search parameters
type DecisionSearchData struct {
	ArananKelime       string `json:"aranan<PERSON>elime"`
	YargitayMah        string `json:"yargitayMah"`
	Hukuk              string `json:"hukuk"`
	Ceza               string `json:"ceza"`
	EsasYil            string `json:"esasYil"`
	EsasIlkSiraNo      string `json:"esasIlkSiraNo"`
	EsasSonSiraNo      string `json:"esasSonSiraNo"`
	KararYil           string `json:"kararYil"`
	KararIlkSiraNo     string `json:"kararIlkSiraNo"`
	KararSonSiraNo     string `json:"kararSonSiraNo"`
	BaslangicTarihi    string `json:"baslangicTarihi"`
	BitisTarihi        string `json:"bitisTarihi"`
	Sirala<PERSON>           string `json:"siralama"`
	SiralamaDirection  string `json:"siralamaDirection"`
	BirimYrgKurulDaire string `json:"birimYrgKurulDaire"`
	BirimYrgHukukDaire string `json:"birimYrgHukukDaire"`
	BirimYrgCezaDaire  string `json:"birimYrgCezaDaire"`
	PageSize           int    `json:"pageSize"`
	PageNumber         int    `json:"pageNumber"`
}

// DecisionSearchResponse represents the API response
type DecisionSearchResponse struct {
	Data DecisionSearchResponseData `json:"data"`
}

// DecisionSearchResponseData represents the response data
type DecisionSearchResponseData struct {
	Data []DecisionItem `json:"data"`
}

// DecisionItem represents a single decision item
type DecisionItem struct {
	ID string `json:"id"`
}

// DecisionHTMLResponse represents the HTML response
type DecisionHTMLResponse struct {
	Data string `json:"data"`
}

// GoDecisionFetcher handles decision fetching operations
type GoDecisionFetcher struct {
	client *http.Client
	proxy  *url.URL
	config *ScrapingConfig
}

// NewGoDecisionFetcher creates a new decision fetcher
func NewGoDecisionFetcher(proxyURL string) *GoDecisionFetcher {
	// Create transport with realistic settings
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}

	// Add proxy if provided
	var proxy *url.URL
	if proxyURL != "" {
		var err error
		proxy, err = url.Parse(proxyURL)
		if err != nil {
			log.Printf("Invalid proxy URL: %v", err)
		} else {
			transport.Proxy = http.ProxyURL(proxy)
		}
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   45 * time.Second, // Increased timeout
	}

	return &GoDecisionFetcher{
		client: client,
		proxy:  proxy,
		config: NewDefaultScrapingConfig(),
	}
}

// NewGoDecisionFetcherWithConfig creates a new decision fetcher with custom config
func NewGoDecisionFetcherWithConfig(proxyURL string, config *ScrapingConfig) *GoDecisionFetcher {
	// Create transport with realistic settings
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}

	var proxy *url.URL
	timeout := config.DirectTimeout
	if proxyURL != "" {
		var err error
		proxy, err = url.Parse(proxyURL)
		if err != nil {
			log.Printf("Invalid proxy URL: %v", err)
		} else {
			transport.Proxy = http.ProxyURL(proxy)
			timeout = config.ProxyTimeout
		}
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	return &GoDecisionFetcher{
		client: client,
		proxy:  proxy,
		config: config,
	}
}

// getRandomUserAgent returns a random user agent from the config
func (df *GoDecisionFetcher) getRandomUserAgent() string {
	if !df.config.EnableRandomUserAgent || len(df.config.UserAgents) == 0 {
		return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
	}

	// Simple random selection based on current time
	index := int(time.Now().UnixNano()) % len(df.config.UserAgents)
	return df.config.UserAgents[index]
}

// getRandomDelay returns a random delay if enabled
func (df *GoDecisionFetcher) getRandomDelay() time.Duration {
	if !df.config.EnableRandomDelay {
		return df.config.BaseDelay
	}

	// Random delay between BaseDelay and BaseDelay*2
	baseMs := df.config.BaseDelay.Milliseconds()
	randomMs := baseMs + (int64(time.Now().UnixNano()) % baseMs)
	return time.Duration(randomMs) * time.Millisecond
}

// GetAllKurullar returns all kurullar
func (df *GoDecisionFetcher) GetAllKurullar() []string {
	return []string{
		"Büyük Genel Kurulu",
		"Hukuk Genel Kurulu",
		"Ceza Genel Kurulu",
		"Hukuk Daireleri Başkanlar Kurulu",
		"Ceza Daireleri Başkanlar Kurulu",
	}
}

// GetAllHukukDaireleri returns all hukuk daireleri
func (df *GoDecisionFetcher) GetAllHukukDaireleri() []string {
	var daireler []string
	for i := 1; i <= 23; i++ {
		daireler = append(daireler, fmt.Sprintf("%d. Hukuk Dairesi", i))
	}
	return daireler
}

// GetAllCezaDaireleri returns all ceza daireleri
func (df *GoDecisionFetcher) GetAllCezaDaireleri() []string {
	var daireler []string
	for i := 1; i <= 23; i++ {
		daireler = append(daireler, fmt.Sprintf("%d. Ceza Dairesi", i))
	}
	return daireler
}

// GetDecisionIDs fetches decision IDs from the API
func (df *GoDecisionFetcher) GetDecisionIDs(pageNumber, pageSize int, maxRetries int) ([]string, error) {
	url := "https://karararama.yargitay.gov.tr/aramadetaylist"

	// Prepare request payload
	request := DecisionSearchRequest{
		Data: DecisionSearchData{
			ArananKelime:       "",
			YargitayMah:        "",
			Hukuk:              "",
			Ceza:               "",
			EsasYil:            "",
			EsasIlkSiraNo:      "",
			EsasSonSiraNo:      "",
			KararYil:           "",
			KararIlkSiraNo:     "",
			KararSonSiraNo:     "",
			BaslangicTarihi:    "",
			BitisTarihi:        "",
			Siralama:           "1",
			SiralamaDirection:  "desc",
			BirimYrgKurulDaire: strings.Join(df.GetAllKurullar(), "+"),
			BirimYrgHukukDaire: strings.Join(df.GetAllHukukDaireleri(), "+"),
			BirimYrgCezaDaire:  strings.Join(df.GetAllCezaDaireleri(), "+"),
			PageSize:           pageSize,
			PageNumber:         pageNumber,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Retry mechanism
	for attempt := 0; attempt < maxRetries; attempt++ {
		// Create HTTP request
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", df.getRandomUserAgent())

		// Make request
		resp, err := df.client.Do(req)
		if err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Page %d: Request error (attempt %d/%d): %v", pageNumber, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second) // Exponential backoff
				continue
			}
			return nil, fmt.Errorf("failed to make request after %d attempts: %w", maxRetries, err)
		}
		defer resp.Body.Close()

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Page %d: Failed to read response (attempt %d/%d): %v", pageNumber, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return nil, fmt.Errorf("failed to read response: %w", err)
		}

		// Parse JSON response
		var response DecisionSearchResponse
		if err := json.Unmarshal(body, &response); err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Page %d: JSON parse error (attempt %d/%d): %v", pageNumber, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return nil, fmt.Errorf("failed to parse JSON response: %w", err)
		}

		// Validate response
		if response.Data.Data == nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Page %d: No data in response (attempt %d/%d)", pageNumber, attempt+1, maxRetries)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return nil, fmt.Errorf("no data in API response")
		}

		// Extract decision IDs
		var decisionIDs []string
		for _, item := range response.Data.Data {
			if item.ID != "" {
				decisionIDs = append(decisionIDs, item.ID)
			}
		}

		log.Printf("✅ Page %d: Retrieved %d decision IDs", pageNumber, len(decisionIDs))
		return decisionIDs, nil
	}

	return nil, fmt.Errorf("failed to get decision IDs after %d attempts", maxRetries)
}

// GetDecisionHTML fetches HTML content for a specific decision
func (df *GoDecisionFetcher) GetDecisionHTML(decisionID string, maxRetries int) (string, error) {
	url := fmt.Sprintf("https://karararama.yargitay.gov.tr/getDokuman?id=%s", decisionID)

	// Retry mechanism
	for attempt := 0; attempt < maxRetries; attempt++ {
		// Create HTTP request
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			return "", fmt.Errorf("failed to create request: %w", err)
		}

		// Set headers
		req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
		req.Header.Set("Accept", "application/json")

		// Make request
		resp, err := df.client.Do(req)
		if err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Decision %s: Request error (attempt %d/%d): %v", decisionID, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("failed to make request after %d attempts: %w", maxRetries, err)
		}
		defer resp.Body.Close()

		// Read response
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Decision %s: Failed to read response (attempt %d/%d): %v", decisionID, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("failed to read response: %w", err)
		}

		// Parse JSON response
		var response DecisionHTMLResponse
		if err := json.Unmarshal(body, &response); err != nil {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Decision %s: JSON parse error (attempt %d/%d): %v", decisionID, attempt+1, maxRetries, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("failed to parse JSON response: %w", err)
		}

		// Validate response
		if response.Data == "" {
			if attempt < maxRetries-1 {
				log.Printf("⚠️ Decision %s: No HTML content (attempt %d/%d)", decisionID, attempt+1, maxRetries)
				time.Sleep(time.Duration(1<<attempt) * time.Second)
				continue
			}
			return "", fmt.Errorf("no HTML content in response")
		}

		log.Printf("✅ Decision %s: Retrieved HTML content (%d chars)", decisionID, len(response.Data))
		return response.Data, nil
	}

	return "", fmt.Errorf("failed to get decision HTML after %d attempts", maxRetries)
}

// TestDecisionFetcher tests the decision fetcher
func TestDecisionFetcher() {
	log.Println("🧪 Testing Go Decision Fetcher...")

	// Create fetcher
	fetcher := NewGoDecisionFetcher("")

	// Test getting decision IDs
	log.Println("📋 Testing decision ID retrieval...")
	decisionIDs, err := fetcher.GetDecisionIDs(1, 5, 3)
	if err != nil {
		log.Printf("❌ Failed to get decision IDs: %v", err)
		return
	}

	log.Printf("✅ Retrieved %d decision IDs", len(decisionIDs))
	if len(decisionIDs) > 0 {
		log.Printf("📋 Sample IDs: %v", decisionIDs[:min(3, len(decisionIDs))])

		// Test getting HTML for first decision
		log.Println("📄 Testing decision HTML retrieval...")
		html, err := fetcher.GetDecisionHTML(decisionIDs[0], 3)
		if err != nil {
			log.Printf("❌ Failed to get decision HTML: %v", err)
			return
		}

		log.Printf("✅ Retrieved HTML for decision %s (%d chars)", decisionIDs[0], len(html))
		log.Printf("📄 First 100 chars: %s...", html[:min(100, len(html))])
	}

	log.Println("✅ Go Decision Fetcher test completed successfully!")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
