package main

import (
	"time"

	"github.com/google/uuid"
)

// DecisionID represents a decision ID in the ids table
type DecisionID struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(255)"`
	IsPull    bool      `json:"is_pull" gorm:"default:false"`
	Page      int       `json:"page"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName sets the table name for DecisionID
func (DecisionID) TableName() string {
	return "ids"
}

// Document represents a document in the docs table
type Document struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	DecisionID string    `json:"decision_id" gorm:"type:varchar(255);not null"`
	Data       string    `json:"data" gorm:"type:text"`
	IsPross    bool      `json:"is_pross" gorm:"default:false"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName sets the table name for Document
func (Document) TableName() string {
	return "docs"
}

// YargitayResponse represents the response from Yargitay API
type YargitayResponse struct {
	Data     YargitayData     `json:"data"`
	Metadata YargitayMetadata `json:"metadata"`
}

type YargitayData struct {
	Data            []YargitayDecision `json:"data"`
	Draw            int                `json:"draw"`
	RecordsTotal    int                `json:"recordsTotal"`
	RecordsFiltered int                `json:"recordsFiltered"`
}

type YargitayDecision struct {
	ID           string `json:"id"`
	Daire        string `json:"daire"`
	EsasNo       string `json:"esasNo"`
	KararNo      string `json:"kararNo"`
	KararTarihi  string `json:"kararTarihi"`
	ArananKelime string `json:"arananKelime"`
	Index        int    `json:"index"`
	SiraNo       int    `json:"siraNo"`
}

type YargitayMetadata struct {
	FMTY string `json:"FMTY"`
	FMC  string `json:"FMC"`
	FMTE string `json:"FMTE"`
	FMU  string `json:"FMU"`
	PTID string `json:"PTID"`
	TID  string `json:"TID"`
	SID  string `json:"SID"`
}

// YargitayDocumentResponse represents the response from document API
type YargitayDocumentResponse struct {
	Data     string           `json:"data"`
	Metadata YargitayMetadata `json:"metadata"`
}

// API Response types
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type ServiceStatus struct {
	IsRunning      bool   `json:"is_running"`
	TotalProcessed int    `json:"total_processed"`
	CurrentPage    int    `json:"current_page"`
	LastError      string `json:"last_error,omitempty"`
}
