package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"
)

type DocumentService struct {
	db       *Database
	isRunning bool
	stopChan  chan bool
	status    ServiceStatus
	mutex     sync.RWMutex
}

func NewDocumentService(db *Database) *DocumentService {
	return &DocumentService{
		db:       db,
		isRunning: false,
		stopChan:  make(chan bool),
		status: ServiceStatus{
			IsRunning:      false,
			TotalProcessed: 0,
			CurrentPage:    0,
		},
	}
}

func (s *DocumentService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

func (s *DocumentService) GetStatus() ServiceStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.status
}

func (s *DocumentService) Start() {
	s.mutex.Lock()
	if s.isRunning {
		s.mutex.Unlock()
		return
	}
	s.isRunning = true
	s.status.IsRunning = true
	s.stopChan = make(chan bool)
	s.mutex.Unlock()

	log.Println("🚀 Document fetching service started")

	for {
		select {
		case <-s.stopChan:
			log.Println("⏹️ Document fetching service stopped")
			s.mutex.Lock()
			s.isRunning = false
			s.status.IsRunning = false
			s.mutex.Unlock()
			return
		default:
			// Get unprocessed decision IDs (is_pull = false)
			decisions, err := s.db.GetUnprocessedDecisionIDs(10) // Process 10 at a time
			if err != nil {
				log.Printf("❌ Error getting unprocessed decision IDs: %v", err)
				s.updateLastError(err.Error())
				time.Sleep(5 * time.Second)
				continue
			}

			if len(decisions) == 0 {
				log.Println("ℹ️ No unprocessed decision IDs found, waiting...")
				time.Sleep(10 * time.Second)
				continue
			}

			// Process each decision
			for _, decision := range decisions {
				select {
				case <-s.stopChan:
					return
				default:
					if err := s.fetchDocument(decision.ID); err != nil {
						log.Printf("❌ Error fetching document for ID %s: %v", decision.ID, err)
						s.updateLastError(err.Error())
						continue
					}

					// Mark as pulled
					if err := s.db.MarkDecisionAsPulled(decision.ID); err != nil {
						log.Printf("⚠️ Failed to mark decision %s as pulled: %v", decision.ID, err)
					}

					s.updateTotalProcessed(1)
					time.Sleep(1 * time.Second) // Rate limiting
				}
			}
		}
	}
}

func (s *DocumentService) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.isRunning {
		return
	}
	
	close(s.stopChan)
}

func (s *DocumentService) fetchDocument(decisionID string) error {
	// Make HTTP request to get document
	url := fmt.Sprintf("https://karararama.yargitay.gov.tr/getDokuman?id=%s", decisionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// Parse response
	var docResp YargitayDocumentResponse
	if err := json.NewDecoder(resp.Body).Decode(&docResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	// Check if response is successful
	if docResp.Metadata.FMTY != "SUCCESS" {
		return fmt.Errorf("API returned error: %s", docResp.Metadata.FMTE)
	}

	// Save document to database
	if err := s.db.SaveDocument(decisionID, docResp.Data); err != nil {
		return fmt.Errorf("failed to save document: %w", err)
	}

	log.Printf("✅ Successfully fetched and saved document for ID: %s", decisionID)
	return nil
}

func (s *DocumentService) updateTotalProcessed(count int) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status.TotalProcessed += count
}

func (s *DocumentService) updateLastError(error string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status.LastError = error
}
