package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"
)

type IDService struct {
	db       *Database
	isRunning bool
	stopChan  chan bool
	status    ServiceStatus
	mutex     sync.RWMutex
}

func NewIDService(db *Database) *IDService {
	return &IDService{
		db:       db,
		isRunning: false,
		stopChan:  make(chan bool),
		status: ServiceStatus{
			IsRunning:      false,
			TotalProcessed: 0,
			CurrentPage:    0,
		},
	}
}

func (s *IDService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

func (s *IDService) GetStatus() ServiceStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.status
}

func (s *IDService) Start() {
	s.mutex.Lock()
	if s.isRunning {
		s.mutex.Unlock()
		return
	}
	s.isRunning = true
	s.status.IsRunning = true
	s.stopChan = make(chan bool)
	s.mutex.Unlock()

	log.Println("🚀 ID fetching service started")

	// Get the last processed page from database
	lastPage, err := s.db.GetLastProcessedPage()
	if err != nil {
		log.Printf("❌ Error getting last processed page: %v", err)
		lastPage = 0
	}

	currentPage := lastPage + 1
	s.updateCurrentPage(currentPage)

	for {
		select {
		case <-s.stopChan:
			log.Println("⏹️ ID fetching service stopped")
			s.mutex.Lock()
			s.isRunning = false
			s.status.IsRunning = false
			s.mutex.Unlock()
			return
		default:
			if err := s.fetchIDsFromPage(currentPage); err != nil {
				log.Printf("❌ Error fetching IDs from page %d: %v", currentPage, err)
				s.updateLastError(err.Error())
				time.Sleep(5 * time.Second) // Wait before retrying
				continue
			}

			currentPage++
			s.updateCurrentPage(currentPage)
			time.Sleep(1 * time.Second) // Rate limiting
		}
	}
}

func (s *IDService) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.isRunning {
		return
	}
	
	close(s.stopChan)
}

func (s *IDService) fetchIDsFromPage(pageNumber int) error {
	// Create the payload for Yargitay API
	payload := map[string]interface{}{
		"data": map[string]interface{}{
			"arananKelime":     "",
			"yargitayMah":      "Büyük Genel Kurulu",
			"hukuk":            "23. Hukuk Dairesi",
			"ceza":             "23. Ceza Dairesi",
			"esasYil":          "",
			"esasIlkSiraNo":    "",
			"esasSonSiraNo":    "",
			"kararYil":         "",
			"kararIlkSiraNo":   "",
			"kararSonSiraNo":   "",
			"baslangicTarihi":  "",
			"bitisTarihi":      "",
			"siralama":         "1",
			"siralamaDirection": "desc",
			"birimYrgKurulDaire": "Hukuk Genel Kurulu+Ceza Genel Kurulu+Ceza Daireleri Başkanlar Kurulu+Hukuk Daireleri Başkanlar Kurulu+Büyük Genel Kurulu",
			"birimYrgHukukDaire": "1. Hukuk Dairesi+2. Hukuk Dairesi+3. Hukuk Dairesi+4. Hukuk Dairesi+5. Hukuk Dairesi+6. Hukuk Dairesi+7. Hukuk Dairesi+8. Hukuk Dairesi+9. Hukuk Dairesi+10. Hukuk Dairesi+11. Hukuk Dairesi+12. Hukuk Dairesi+13. Hukuk Dairesi+14. Hukuk Dairesi+15. Hukuk Dairesi+16. Hukuk Dairesi+17. Hukuk Dairesi+18. Hukuk Dairesi+19. Hukuk Dairesi+20. Hukuk Dairesi+21. Hukuk Dairesi+22. Hukuk Dairesi+23. Hukuk Dairesi",
			"birimYrgCezaDaire": "1. Ceza Dairesi+2. Ceza Dairesi+3. Ceza Dairesi+4. Ceza Dairesi+5. Ceza Dairesi+6. Ceza Dairesi+7. Ceza Dairesi+8. Ceza Dairesi+9. Ceza Dairesi+10. Ceza Dairesi+11. Ceza Dairesi+12. Ceza Dairesi+13. Ceza Dairesi+14. Ceza Dairesi+15. Ceza Dairesi+16. Ceza Dairesi+17. Ceza Dairesi+18. Ceza Dairesi+19. Ceza Dairesi+20. Ceza Dairesi+21. Ceza Dairesi+22. Ceza Dairesi+23. Ceza Dairesi",
			"pageSize":         10,
			"pageNumber":       pageNumber,
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Make HTTP request
	resp, err := http.Post("https://karararama.yargitay.gov.tr/aramadetaylist", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// Parse response
	var yargitayResp YargitayResponse
	if err := json.NewDecoder(resp.Body).Decode(&yargitayResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	// Check if response is successful
	if yargitayResp.Metadata.FMTY != "SUCCESS" {
		return fmt.Errorf("API returned error: %s", yargitayResp.Metadata.FMTE)
	}

	// Save IDs to database
	savedCount := 0
	for _, decision := range yargitayResp.Data.Data {
		if err := s.db.SaveDecisionID(decision.ID, pageNumber); err != nil {
			log.Printf("⚠️ Failed to save decision ID %s: %v", decision.ID, err)
			continue
		}
		savedCount++
	}

	s.updateTotalProcessed(savedCount)
	log.Printf("✅ Page %d: Saved %d/%d decision IDs", pageNumber, savedCount, len(yargitayResp.Data.Data))

	return nil
}

func (s *IDService) updateCurrentPage(page int) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status.CurrentPage = page
}

func (s *IDService) updateTotalProcessed(count int) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status.TotalProcessed += count
}

func (s *IDService) updateLastError(error string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.status.LastError = error
}
