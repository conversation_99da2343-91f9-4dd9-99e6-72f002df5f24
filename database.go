package main

import (
	"fmt"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type Database struct {
	db *gorm.DB
}

func NewDatabase() (*Database, error) {
	// Get database connection string from environment
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "supreme_court_decisions")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	database := &Database{db: db}

	// Auto migrate the schema
	if err := database.autoMigrate(); err != nil {
		return nil, fmt.Errorf("failed to auto migrate: %w", err)
	}

	return database, nil
}

func (d *Database) autoMigrate() error {
	// Auto migrate all models
	return d.db.AutoMigrate(&DecisionID{}, &Document{})
}

// SaveDecisionID saves a decision ID to the ids table
func (d *Database) SaveDecisionID(decisionID string, page int) error {
	decision := DecisionID{
		ID:     decisionID,
		IsPull: false,
		Page:   page,
	}

	// Use FirstOrCreate to avoid duplicates
	result := d.db.Where("id = ?", decisionID).FirstOrCreate(&decision)
	return result.Error
}

// GetLastProcessedPage returns the highest page number from ids table
func (d *Database) GetLastProcessedPage() (int, error) {
	var result struct {
		MaxPage int
	}

	err := d.db.Model(&DecisionID{}).Select("COALESCE(MAX(page), 0) as max_page").Scan(&result).Error
	return result.MaxPage, err
}

// GetUnprocessedDecisionIDs returns decision IDs that haven't been pulled yet
func (d *Database) GetUnprocessedDecisionIDs(limit int) ([]DecisionID, error) {
	var decisions []DecisionID

	err := d.db.Where("is_pull = ?", false).
		Order("page, id").
		Limit(limit).
		Find(&decisions).Error

	return decisions, err
}

// SaveDocument saves a document to the docs table
func (d *Database) SaveDocument(decisionID, data string) error {
	document := Document{
		DecisionID: decisionID,
		Data:       data,
		IsPross:    false,
	}

	return d.db.Create(&document).Error
}

// MarkDecisionAsPulled marks a decision ID as pulled
func (d *Database) MarkDecisionAsPulled(decisionID string) error {
	return d.db.Model(&DecisionID{}).
		Where("id = ?", decisionID).
		Update("is_pull", true).Error
}

// GetUnprocessedCount returns count of decision IDs that haven't been pulled yet
func (d *Database) GetUnprocessedCount() (int, error) {
	var count int64
	err := d.db.Model(&DecisionID{}).Where("is_pull = ?", false).Count(&count).Error
	return int(count), err
}

func (d *Database) Close() error {
	sqlDB, err := d.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
