package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

var (
	idService  *IDService
	docService *DocumentService
	db         *Database
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Initialize database
	var err error
	db, err = NewDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize services
	idService = NewIDService(db)
	docService = NewDocumentService(db)

	// Setup Gin router
	r := gin.Default()

	// Add CORS middleware
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Routes
	r.POST("/ids/start", startIDFetchingHandler)
	r.POST("/ids/stop", stopIDFetchingHandler)
	r.POST("/docs/start", startDocFetchingHandler)
	r.POST("/docs/stop", stopDocFetchingHandler)
	r.GET("/status", statusHandler)

	// Start server
	port := getEnv("PORT", "8080")
	log.Printf("🚀 Starting server on port %s", port)
	log.Fatal(r.Run(":" + port))
}

// Handler functions
func startIDFetchingHandler(c *gin.Context) {
	if idService.IsRunning() {
		c.JSON(http.StatusConflict, APIResponse{
			Success: false,
			Message: "ID fetching is already running",
		})
		return
	}

	go idService.Start()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "ID fetching started",
	})
}

func stopIDFetchingHandler(c *gin.Context) {
	if !idService.IsRunning() {
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Message: "ID fetching is not running",
		})
		return
	}

	idService.Stop()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "ID fetching stopped",
	})
}

func startDocFetchingHandler(c *gin.Context) {
	if docService.IsRunning() {
		c.JSON(http.StatusConflict, APIResponse{
			Success: false,
			Message: "Document fetching is already running",
		})
		return
	}

	go docService.Start()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Document fetching started",
	})
}

func stopDocFetchingHandler(c *gin.Context) {
	if !docService.IsRunning() {
		c.JSON(http.StatusOK, APIResponse{
			Success: true,
			Message: "Document fetching is not running",
		})
		return
	}

	docService.Stop()

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Message: "Document fetching stopped",
	})
}

func statusHandler(c *gin.Context) {
	idStatus := idService.GetStatus()
	docStatus := docService.GetStatus()

	c.JSON(http.StatusOK, gin.H{
		"id_service":  idStatus,
		"doc_service": docStatus,
	})
}
