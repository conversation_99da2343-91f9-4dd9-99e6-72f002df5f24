version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: go-scraper-db
    environment:
      POSTGRES_DB: go-scraper
      POSTGRES_USER: go-scraper
      POSTGRES_PASSWORD: go-scraper
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5421:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U go-scraper -d go-scraper"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - go-scraper

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: go-scraper
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "9001:9001"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=go-scraper
      - DB_PASSWORD=go-scraper
      - DB_NAME=go-scraper
      - PORT=9001
      - GIN_MODE=release
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9001/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - go-scraper
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  postgres_data:
    driver: local

networks:
  go-scraper:
    driver: bridge
