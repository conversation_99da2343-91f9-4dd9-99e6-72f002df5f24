package routes

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"sync"
	"time"
)

// ProxyManager manages working proxies for decision scraping
type ProxyManager struct {
	workingProxies []ProxyTestResult
	mutex          sync.RWMutex
	lastUpdate     time.Time
	updateInterval time.Duration
}

// NewProxyManager creates a new proxy manager
func NewProxyManager() *ProxyManager {
	return &ProxyManager{
		workingProxies: make([]ProxyTestResult, 0),
		updateInterval: 5 * time.Minute, // Update proxies every 5 minutes
	}
}

// GetWorkingProxies fetches and tests proxies, returns only working ones
func (pm *ProxyManager) GetWorkingProxies(testURL string, timeout string) ([]ProxyTestResult, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// Check if we need to update proxies
	if time.Since(pm.lastUpdate) < pm.updateInterval && len(pm.workingProxies) > 0 {
		log.Printf("Using cached proxies (%d available)", len(pm.workingProxies))
		return pm.workingProxies, nil
	}

	log.Println("Fetching fresh proxies from ProxyScrape API")

	// Get proxies from ProxyScrape API
	apiURL := "https://api.proxyscrape.com/v4/free-proxy-list/get"
	fullURL := fmt.Sprintf("%s?request=get_proxies&skip=0&proxy_format=protocolipport&format=json&limit=50", apiURL)

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get proxies: %v", err)
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// Parse the response
	var apiResponse struct {
		Proxies []ProxyInfo `json:"proxies"`
	}

	err = json.Unmarshal(responseBody, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	log.Printf("Testing %d proxies for working ones", len(apiResponse.Proxies))

	// Test proxies concurrently
	results := make([]ProxyTestResult, len(apiResponse.Proxies))
	var wg sync.WaitGroup

	for i, proxy := range apiResponse.Proxies {
		wg.Add(1)
		go func(index int, p ProxyInfo) {
			defer wg.Done()
			results[index] = testSingleProxy(p, testURL, timeout)
		}(i, proxy)
	}

	wg.Wait()

	// Filter only working proxies
	var workingProxies []ProxyTestResult
	for _, result := range results {
		if result.Working {
			workingProxies = append(workingProxies, result)
		}
	}

	pm.workingProxies = workingProxies
	pm.lastUpdate = time.Now()

	log.Printf("Found %d working proxies out of %d tested", len(workingProxies), len(results))
	return workingProxies, nil
}

// GetProxyForBatch returns a proxy for a specific batch index
func (pm *ProxyManager) GetProxyForBatch(batchIndex int) (*ProxyTestResult, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.workingProxies) == 0 {
		return nil, fmt.Errorf("no working proxies available")
	}

	// Round-robin assignment
	proxyIndex := batchIndex % len(pm.workingProxies)
	return &pm.workingProxies[proxyIndex], nil
}

// GetProxyCount returns the number of working proxies
func (pm *ProxyManager) GetProxyCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.workingProxies)
}

// RefreshProxies forces a refresh of the proxy list
func (pm *ProxyManager) RefreshProxies(testURL string, timeout string) error {
	pm.mutex.Lock()
	pm.lastUpdate = time.Time{} // Force refresh
	pm.mutex.Unlock()

	_, err := pm.GetWorkingProxies(testURL, timeout)
	return err
}

// CreateHTTPClientWithProxy creates an HTTP client configured to use a specific proxy
func (pm *ProxyManager) CreateHTTPClientWithProxy(proxy *ProxyTestResult, timeout time.Duration) (*http.Client, error) {
	if proxy == nil {
		return &http.Client{Timeout: timeout}, nil
	}

	proxyURL, err := url.Parse(proxy.Proxy)
	if err != nil {
		return nil, fmt.Errorf("invalid proxy URL: %v", err)
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	return client, nil
}

// BatchInfo represents information about a batch of decisions
type BatchInfo struct {
	BatchIndex     int              `json:"batch_index"`
	DecisionIDs    []string         `json:"decision_ids"`
	ProxyInfo      *ProxyTestResult `json:"proxy_info"`
	StartTime      time.Time        `json:"start_time"`
	EndTime        *time.Time       `json:"end_time,omitempty"`
	ProcessedCount int              `json:"processed_count"`
	SuccessCount   int              `json:"success_count"`
	ErrorCount     int              `json:"error_count"`
}

// DistributeDecisionsToBatches distributes decision IDs into batches for proxy processing
func (pm *ProxyManager) DistributeDecisionsToBatches(decisionIDs []string, batchSize int) ([]BatchInfo, error) {
	proxyCount := pm.GetProxyCount()
	if proxyCount == 0 {
		return nil, fmt.Errorf("no working proxies available for batch distribution")
	}

	var batches []BatchInfo
	batchIndex := 0

	for i := 0; i < len(decisionIDs); i += batchSize {
		end := i + batchSize
		if end > len(decisionIDs) {
			end = len(decisionIDs)
		}

		batchDecisionIDs := decisionIDs[i:end]
		proxy, err := pm.GetProxyForBatch(batchIndex)
		if err != nil {
			return nil, fmt.Errorf("failed to get proxy for batch %d: %v", batchIndex, err)
		}

		batch := BatchInfo{
			BatchIndex:  batchIndex,
			DecisionIDs: batchDecisionIDs,
			ProxyInfo:   proxy,
			StartTime:   time.Now(),
		}

		batches = append(batches, batch)
		batchIndex++
	}

	log.Printf("Distributed %d decisions into %d batches using %d proxies",
		len(decisionIDs), len(batches), proxyCount)

	return batches, nil
}

// Global proxy manager instance
var globalProxyManager *ProxyManager
var proxyManagerOnce sync.Once

// GetGlobalProxyManager returns the global proxy manager instance
func GetGlobalProxyManager() *ProxyManager {
	proxyManagerOnce.Do(func() {
		globalProxyManager = NewProxyManager()
	})
	return globalProxyManager
}
